<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Reviews Ellipsis Test</title>
    <link rel="stylesheet" href="snapapp.css">
    <style>
        body {
            font-family: 'Amazon Ember', sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 400px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-title {
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-title">Customer Reviews Ellipsis Test</div>
        
        <div class="customer-reviews-card-div">
            <div class="reviews-list">
                <!-- Normal length review -->
                <div class="listing-review-div">
                    <div class="product-div" style="background: #FF6B35;"></div>
                    <div class="listing-content-div">
                        <div class="title-and-marketplace">
                            <img src="./assets/US.svg" alt="US" class="marketplace-flag" width="20" height="20" />
                            <span class="listing-title">Normal Length Title</span>
                        </div>
                        <div class="listing-comment">This is a normal comment that fits well.</div>
                        <div class="rating-row">⭐⭐⭐⭐⭐ (4.0 out of 5)</div>
                        <div class="review-date">Aug 12, 2025</div>
                    </div>
                    <div class="view-btn"><img src="./assets/view-insights-ic.svg" alt="View" width="8" height="8" /><span>View</span></div>
                </div>
                
                <hr class="listing-section-divider">
                
                <!-- Long title and comment review -->
                <div class="listing-review-div">
                    <div class="product-div" style="background: #8E44AD;"></div>
                    <div class="listing-content-div">
                        <div class="title-and-marketplace">
                            <img src="./assets/US.svg" alt="US" class="marketplace-flag" width="20" height="20" />
                            <span class="listing-title">Super Long Product Title That Should Be Truncated With Ellipsis Because It Is Way Too Long To Fit On A Single Line</span>
                        </div>
                        <div class="listing-comment">This is an extremely long customer review comment that should definitely be truncated with ellipsis because it contains way too much text to fit on a single line and would otherwise break the layout of the customer reviews section.</div>
                        <div class="rating-row">⭐⭐⭐⭐⭐ (4.5 out of 5)</div>
                        <div class="review-date">Sep 15, 2025</div>
                    </div>
                    <div class="view-btn"><img src="./assets/view-insights-ic.svg" alt="View" width="8" height="8" /><span>View</span></div>
                </div>
                
                <hr class="listing-section-divider">
                
                <!-- Another long example -->
                <div class="listing-review-div">
                    <div class="product-div" style="background: #2ECC71;"></div>
                    <div class="listing-content-div">
                        <div class="title-and-marketplace">
                            <img src="./assets/UK.svg" alt="UK" class="marketplace-flag" width="20" height="20" />
                            <span class="listing-title">Another Very Very Long Product Title That Tests The Ellipsis Functionality</span>
                        </div>
                        <div class="listing-comment">Yet another long comment to test the ellipsis functionality and ensure it works properly across different lengths.</div>
                        <div class="rating-row">⭐⭐⭐⭐☆ (3.5 out of 5)</div>
                        <div class="review-date">Jul 21, 2025</div>
                    </div>
                    <div class="view-btn"><img src="./assets/view-insights-ic.svg" alt="View" width="8" height="8" /><span>View</span></div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
