<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Fixes Test with Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .status-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .loading {
            color: #007bff;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .warning {
            color: #ffc107;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        #console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔧 Debug Fixes Test with Dashboard Components</h1>
    
    <div class="status-container">
        <h3>Loading Status</h3>
        <div id="loading-status" class="loading">Loading dashboard components...</div>
        <div id="component-status"></div>
    </div>
    
    <div class="status-container">
        <h3>Test Controls</h3>
        <button id="run-quick-test" disabled>Run Quick Test</button>
        <button id="run-full-test" disabled>Run Full Test Suite</button>
        <button id="clear-console">Clear Console</button>
    </div>
    
    <div class="status-container">
        <h3>Console Output</h3>
        <div id="console-output"></div>
    </div>

    <!-- Load dashboard components -->
    <script src="performance-optimizations/event-cleanup-manager.js"></script>
    <script src="performance-optimizations/memory-monitor.js"></script>
    <script src="performance-optimizations/realtime-data-manager.js"></script>
    <script src="components/charts/snap-charts.js"></script>
    <script src="components/dashboard/dashboard.js"></script>

    <script>
        // Console capture
        const originalConsole = {
            log: console.log,
            warn: console.warn,
            error: console.error
        };
        
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : 'ℹ️';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        // Override console methods
        console.log = function(...args) {
            originalConsole.log.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.warn = function(...args) {
            originalConsole.warn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        console.error = function(...args) {
            originalConsole.error.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        // Component loading check
        function checkComponents() {
            const requiredComponents = [
                'RealTimeDataManager',
                'EventCleanupManager', 
                'SnapChart',
                'SnapLogger',
                'createDebouncedFunction',
                'extractUnitsValue',
                'createInsightsModal',
                'createSafeElementFromTrustedHTML'
            ];
            
            const loaded = [];
            const missing = [];
            
            requiredComponents.forEach(comp => {
                if (window[comp]) {
                    loaded.push(comp);
                } else {
                    missing.push(comp);
                }
            });
            
            const statusDiv = document.getElementById('component-status');
            const loadingDiv = document.getElementById('loading-status');
            
            if (missing.length === 0) {
                loadingDiv.textContent = '✅ All components loaded successfully!';
                loadingDiv.className = 'success';
                statusDiv.innerHTML = `<div class="success">✅ Loaded: ${loaded.join(', ')}</div>`;
                
                // Enable test buttons
                document.getElementById('run-quick-test').disabled = false;
                document.getElementById('run-full-test').disabled = false;
                
                console.log('✅ All dashboard components loaded successfully');
                return true;
            } else {
                loadingDiv.textContent = '⚠️ Some components are missing';
                loadingDiv.className = 'warning';
                statusDiv.innerHTML = `
                    <div class="success">✅ Loaded: ${loaded.join(', ')}</div>
                    <div class="error">❌ Missing: ${missing.join(', ')}</div>
                `;
                console.warn('⚠️ Missing components:', missing.join(', '));
                return false;
            }
        }
        
        // Quick test function
        function runQuickTest() {
            console.log('🧪 Running Quick Test...');
            
            // Test 1: Timer Cleanup
            if (window.EventCleanupManager) {
                try {
                    const timeoutId = window.EventCleanupManager.setTimeout(() => {}, 1000);
                    window.EventCleanupManager.clearTimeout(timeoutId);
                    console.log('✅ Timer cleanup: Working');
                } catch (e) {
                    console.error('❌ Timer cleanup: Failed -', e.message);
                }
            }
            
            // Test 2: Logger System
            if (window.SnapLogger) {
                try {
                    const originalLevel = window.SnapLogger.level;
                    window.SnapLogger.setLevel('warn');
                    const testPassed = window.SnapLogger.level === 'warn';
                    window.SnapLogger.setLevel(originalLevel);
                    console.log(testPassed ? '✅ Logger system: Working' : '❌ Logger system: Failed');
                } catch (e) {
                    console.error('❌ Logger system: Failed -', e.message);
                }
            }
            
            // Test 3: Units Parser
            if (window.extractUnitsValue) {
                try {
                    const mockListing = document.createElement('div');
                    const unitsSpan = document.createElement('span');
                    const unitsBadge = document.createElement('div');
                    unitsBadge.className = 'order-units-badge';
                    unitsBadge.appendChild(unitsSpan);
                    mockListing.appendChild(unitsBadge);
                    
                    unitsSpan.textContent = '+1,234';
                    const result = window.extractUnitsValue(mockListing);
                    
                    console.log(result === 1234 ? '✅ Units parser: Working' : `❌ Units parser: Failed (got ${result})`);
                } catch (e) {
                    console.error('❌ Units parser: Failed -', e.message);
                }
            }
            
            // Test 4: XSS Protection
            if (window.createSafeElementFromTrustedHTML) {
                try {
                    const safeHTML = '<div class="test-class">Safe content</div>';
                    const element = window.createSafeElementFromTrustedHTML(safeHTML, 'test-class');
                    
                    const testPassed = element && element.classList.contains('test-class');
                    console.log(testPassed ? '✅ XSS protection: Working' : '❌ XSS protection: Failed');
                } catch (e) {
                    console.error('❌ XSS protection: Failed -', e.message);
                }
            }
            
            // Test 5: View Insights Modal
            if (window.createInsightsModal) {
                try {
                    window.createInsightsModal('sales', 'Test Period');
                    const modal = document.getElementById('insights-modal');
                    
                    if (modal) {
                        console.log('✅ View Insights modal: Working');
                        modal.remove(); // Clean up
                    } else {
                        console.log('❌ View Insights modal: Modal not created');
                    }
                } catch (e) {
                    console.error('❌ View Insights modal: Failed -', e.message);
                }
            }
            
            console.log('🎉 Quick test completed!');
        }
        
        // Full test function
        function runFullTest() {
            console.log('🚀 Running Full Test Suite...');
            
            // Load and run the validation script
            fetch('validate-debug-fixes.js')
                .then(response => response.text())
                .then(script => {
                    eval(script);
                })
                .catch(error => {
                    console.error('❌ Failed to load validation script:', error);
                });
        }
        
        // Event listeners
        document.getElementById('run-quick-test').addEventListener('click', runQuickTest);
        document.getElementById('run-full-test').addEventListener('click', runFullTest);
        document.getElementById('clear-console').addEventListener('click', () => {
            consoleOutput.textContent = '';
        });
        
        // Check components when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkComponents();
            }, 2000); // Give components time to load
        });
        
        // Periodic check for components
        const checkInterval = setInterval(() => {
            if (checkComponents()) {
                clearInterval(checkInterval);
            }
        }, 1000);
    </script>
</body>
</html>
