<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mock Zero Data - Payout Cards & Lifetime Insights</title>
    <link rel="stylesheet" href="snapapp.css">
    <style>
        body {
            font-family: 'Amazon Ember', Arial, sans-serif;
            margin: 20px;
            background-color: var(--bg-primary);
            color: var(--text-primary);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #04AE2C;
            border-radius: 8px;
            background-color: var(--bg-secondary);
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #04AE2C;
        }
        .test-controls {
            margin: 20px 0;
            padding: 15px;
            background-color: var(--bg-tertiary);
            border-radius: 6px;
        }
        .test-button {
            background-color: #04AE2C;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #038a24;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.enabled {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disabled {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .inspection-results {
            margin-top: 20px;
            padding: 15px;
            background-color: var(--bg-quaternary);
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Mock Zero Data Test - Payout Cards & Lifetime Insights</h1>
    
    <div class="test-controls">
        <button class="test-button" onclick="toggleMockZeroData()">Toggle Mock Zero Data</button>
        <button class="test-button" onclick="applyZeroState()">Apply Zero State</button>
        <button class="test-button" onclick="inspectComponents()">Inspect Components</button>
        <button class="test-button" onclick="resetToMockData()">Reset to Mock Data</button>
        <div id="status" class="status enabled">Mock Zero Data: ENABLED</div>
    </div>

    <div class="test-section">
        <div class="test-title">Payout Cards Test</div>
        <div class="payout-cards-row">
            <!-- Next Payout Card -->
            <div class="next-payout-card-div">
                <div class="payout-title-date-div">
                    <div class="title-date-section">
                        <img src="./assets/payout-ic.svg" alt="Payout Icon" class="payout-card-icon" width="16" height="16" />
                        <div class="title-date-text">
                            <span class="payout-card-title">Next Payout</span>
                        </div>
                    </div>
                    <div class="payout-date">Dec 31, 2024</div>
                </div>

                <div class="marketplaces-div">
                    <div class="marketplaces-sales-row">
                        <div class="marketplace-col us active">
                            <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
                            <span class="marketplace-total-sales-count">188</span>
                            <span class="marketplace-total-earned-royalties">$344.18</span>
                            <span class="marketplace-total-returned-units negative">(-7)</span>
                        </div>
                        <div class="marketplace-col uk inactive">
                            <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
                            <span class="marketplace-total-sales-count">45</span>
                            <span class="marketplace-total-earned-royalties">£89.12</span>
                            <span class="marketplace-total-returned-units zero">(0)</span>
                        </div>
                        <div class="marketplace-col de inactive">
                            <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
                            <span class="marketplace-total-sales-count">23</span>
                            <span class="marketplace-total-earned-royalties">€67.45</span>
                            <span class="marketplace-total-returned-units negative">(-2)</span>
                        </div>
                    </div>
                </div>

                <div class="sales-section-divider"></div>

                <div class="no-payout-state" style="display: none;">
                    <img src="./assets/no-payout-img.svg" alt="No Payout" class="no-payout-img" width="98" height="109" />
                    <div class="no-payout-text">
                        <div class="no-payout-title">No payout available, yet.</div>
                        <div class="no-payout-subtitle">Check back soon!</div>
                    </div>
                </div>

                <div class="payout-data-div">
                    <div class="payout-data-item">
                        <span class="payout-data-label">Gross Earnings</span>
                        <span class="payout-data-value gross-earnings">$344.18</span>
                    </div>
                    <div class="payout-data-item">
                        <span class="payout-data-label">Refunds</span>
                        <span class="payout-data-value refunds negative">-$13.67</span>
                    </div>
                    <div class="payout-data-item">
                        <span class="payout-data-label">Net Earnings</span>
                        <span class="payout-data-value net-earnings positive">$31,130.51</span>
                    </div>
                    <div class="payout-data-item taxes-item">
                        <span class="payout-data-label">After Taxes (30%)</span>
                        <span class="payout-data-value after-taxes positive">$29,130.51</span>
                    </div>
                    <div class="payout-data-item">
                        <span class="payout-data-label">Units Charged</span>
                        <span class="payout-data-value units-charged">188</span>
                    </div>
                    <div class="payout-data-item">
                        <span class="payout-data-label">Units Refunded</span>
                        <span class="payout-data-value units-refunded negative">7</span>
                    </div>
                </div>
            </div>

            <!-- Previous Payout Card -->
            <div class="previous-payout-card-div">
                <div class="payout-title-date-div">
                    <div class="title-date-section">
                        <img src="./assets/payout-ic.svg" alt="Payout Icon" class="payout-card-icon" width="16" height="16" />
                        <div class="title-date-text">
                            <span class="payout-card-title">Previous Payout</span>
                        </div>
                    </div>
                    <div class="payout-date">Nov 30, 2024</div>
                </div>

                <div class="marketplaces-div">
                    <div class="marketplaces-sales-row">
                        <div class="marketplace-col us active">
                            <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
                            <span class="marketplace-total-sales-count">165</span>
                            <span class="marketplace-total-earned-royalties">$298.45</span>
                            <span class="marketplace-total-returned-units negative">(-5)</span>
                        </div>
                        <div class="marketplace-col uk inactive">
                            <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
                            <span class="marketplace-total-sales-count">32</span>
                            <span class="marketplace-total-earned-royalties">£67.89</span>
                            <span class="marketplace-total-returned-units zero">(0)</span>
                        </div>
                        <div class="marketplace-col de inactive">
                            <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
                            <span class="marketplace-total-sales-count">18</span>
                            <span class="marketplace-total-earned-royalties">€45.23</span>
                            <span class="marketplace-total-returned-units negative">(-1)</span>
                        </div>
                    </div>
                </div>

                <div class="sales-section-divider"></div>

                <div class="no-payout-state" style="display: none;">
                    <img src="./assets/no-payout-img.svg" alt="No Payout" class="no-payout-img" width="98" height="109" />
                    <div class="no-payout-text">
                        <div class="no-payout-title">No payout available, yet.</div>
                        <div class="no-payout-subtitle">Check back soon!</div>
                    </div>
                </div>

                <div class="payout-data-div">
                    <div class="payout-data-item">
                        <span class="payout-data-label">Gross Earnings</span>
                        <span class="payout-data-value gross-earnings">$298.45</span>
                    </div>
                    <div class="payout-data-item">
                        <span class="payout-data-label">Refunds</span>
                        <span class="payout-data-value refunds negative">-$11.23</span>
                    </div>
                    <div class="payout-data-item">
                        <span class="payout-data-label">Net Earnings</span>
                        <span class="payout-data-value net-earnings positive">$28,945.33</span>
                    </div>
                    <div class="payout-data-item taxes-item">
                        <span class="payout-data-label">After Taxes (30%)</span>
                        <span class="payout-data-value after-taxes positive">$26,945.33</span>
                    </div>
                    <div class="payout-data-item">
                        <span class="payout-data-label">Units Charged</span>
                        <span class="payout-data-value units-charged">165</span>
                    </div>
                    <div class="payout-data-item">
                        <span class="payout-data-label">Units Refunded</span>
                        <span class="payout-data-value units-refunded negative">5</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">Lifetime Insights Card Test</div>
        <div class="dashboard-component">
            <div class="lifetime-insights-card-div">
                <div class="Sales-title-date-div">
                    <div class="title-date-section">
                        <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
                        <div class="title-date-text">
                            <span class="sales-card-title">Lifetime Insights</span>
                        </div>
                    </div>
                    <div class="view-insights-btn">
                        <span>View Insights</span>
                        <img src="./assets/view-insights-ic.svg" alt="View Insights" width="8" height="8" />
                    </div>
                </div>
                
                <!-- Figma-accurate sales-analytics-div -->
                <div class="sales-analytics-div">
                    <div class="top-row">
                        <div class="sales-count-div">
                            <span class="sales-count">217,223</span>
                        </div>
                    </div>
                    <div class="analytics-div">
                        <div class="metric-col royalties-metric">
                            <div class="metric-top">
                                <img src="./assets/data-cell-ic.svg" alt="Royalties Icon" class="metric-icon" width="12" height="12" />
                                <span class="metric-value royalties">$511,933.0</span>
                            </div>
                            <span class="metric-label">Royalties</span>
                        </div>
                        <div class="metric-divider"></div>
                        <div class="metric-col returned-metric">
                            <div class="metric-top">
                                <img src="./assets/data-cell-ic.svg" alt="Returned Icon" class="metric-icon" width="12" height="12" />
                                <span class="metric-value returned">17,099</span>
                            </div>
                            <div class="metric-label-row">
                                <span class="metric-label">Returned</span>
                                <span class="metric-percentage returned-percentage">7.9%</span>
                            </div>
                        </div>
                        <div class="metric-divider"></div>
                        <div class="metric-col cancelled-metric">
                            <div class="metric-top">
                                <img src="./assets/data-cell-ic.svg" alt="Cancelled Icon" class="metric-icon" width="12" height="12" />
                                <span class="metric-value cancelled">2,231</span>
                            </div>
                            <span class="metric-label">Cancelled</span>
                        </div>
                        <div class="metric-divider"></div>
                        <div class="metric-col ads-metric">
                            <div class="metric-top">
                                <img src="./assets/data-cell-ic.svg" alt="Ads Icon" class="metric-icon" width="12" height="12" />
                                <span class="metric-value ads">65,000</span>
                            </div>
                            <div class="metric-label-row">
                                <span class="metric-label">Ads</span>
                                <span class="metric-percentage ads-percentage">29.9%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="lifetime-data-div">
                    <div class="lifetime-data-item">
                        <span class="lifetime-data-label">Revenue</span>
                        <span class="lifetime-data-value revenue">$1,571,933.0</span>
                    </div>
                    <div class="lifetime-data-item">
                        <span class="lifetime-data-label">Net Earnings</span>
                        <span class="lifetime-data-value positive">$511,933.0</span>
                    </div>
                    <div class="lifetime-data-item">
                        <span class="lifetime-data-label">Gross Earnings</span>
                        <span class="lifetime-data-value gross-earnings">$571,933.0</span>
                    </div>
                    <div class="lifetime-data-item">
                        <span class="lifetime-data-label">Taxes (US <span class="lifetime-tax-rate">30%</span>)</span>
                        <span class="lifetime-data-value negative">-$61,933</span>
                    </div>
                    <div class="lifetime-data-item">
                        <span class="lifetime-data-label">Refunds</span>
                        <span class="lifetime-data-value negative">-$61,933</span>
                    </div>
                    <div class="lifetime-data-item">
                        <span class="lifetime-data-label">Royalty/Sale</span>
                        <span class="lifetime-data-value positive">$3.93</span>
                    </div>
                </div>

                <div class="marketplaces-div">
                    <div class="marketplaces-sales-row">
                        <div class="marketplace-col us">
                            <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
                            <span class="marketplace-total-sales-count">190,153</span>
                            <span class="marketplace-total-earned-royalties">$490,933.0</span>
                            <span class="marketplace-total-returned-units negative">(-11,143)</span>
                        </div>
                        <div class="marketplace-col uk">
                            <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
                            <span class="marketplace-total-sales-count">11,223</span>
                            <span class="marketplace-total-earned-royalties">£11,409.0</span>
                            <span class="marketplace-total-returned-units zero">(0)</span>
                        </div>
                        <div class="marketplace-col de">
                            <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
                            <span class="marketplace-total-sales-count">31,232</span>
                            <span class="marketplace-total-earned-royalties">€8,021.0</span>
                            <span class="marketplace-total-returned-units negative">(-1,934)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="inspection-results" class="inspection-results" style="display: none;"></div>

    <!-- Load mock zero data utility -->
    <script src="utils/mock-zero-data.js"></script>
    
    <script>
        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
            
            // Wrap the test components in dashboard-component for the mock zero data to work
            if (!document.querySelector('.dashboard-component')) {
                const payoutSection = document.querySelector('.payout-cards-row');
                if (payoutSection && !payoutSection.closest('.dashboard-component')) {
                    const wrapper = document.createElement('div');
                    wrapper.className = 'dashboard-component';
                    payoutSection.parentNode.insertBefore(wrapper, payoutSection);
                    wrapper.appendChild(payoutSection);
                }
            }
        });

        function toggleMockZeroData() {
            window.USE_MOCK_ZERO_DATA = !window.USE_MOCK_ZERO_DATA;
            updateStatus();
            
            if (window.USE_MOCK_ZERO_DATA) {
                applyZeroState();
            } else {
                resetToMockData();
            }
        }

        function applyZeroState() {
            if (window.MockZeroData) {
                window.MockZeroData.applyDashboardZeroState();
                console.log('✅ Applied mock zero data state');
            } else {
                console.error('❌ MockZeroData not available');
            }
        }

        function updateStatus() {
            const statusEl = document.getElementById('status');
            const isEnabled = window.USE_MOCK_ZERO_DATA;
            statusEl.textContent = `Mock Zero Data: ${isEnabled ? 'ENABLED' : 'DISABLED'}`;
            statusEl.className = `status ${isEnabled ? 'enabled' : 'disabled'}`;
        }

        function resetToMockData() {
            // Reset payout cards
            const payoutCards = document.querySelectorAll('.next-payout-card-div, .previous-payout-card-div');
            payoutCards.forEach(card => {
                const emptyState = card.querySelector('.no-payout-state');
                const marketplacesDiv = card.querySelector('.marketplaces-div');
                const payoutDataDiv = card.querySelector('.payout-data-div');
                const divider = card.querySelector('.sales-section-divider');
                
                if (emptyState) emptyState.style.display = 'none';
                if (marketplacesDiv) marketplacesDiv.style.display = '';
                if (payoutDataDiv) payoutDataDiv.style.display = '';
                if (divider) divider.style.display = '';
            });

            // Reset specific values (simplified reset - in real app this would restore from original data)
            const nextCard = document.querySelector('.next-payout-card-div');
            if (nextCard) {
                nextCard.querySelector('.gross-earnings').textContent = '$344.18';
                nextCard.querySelector('.refunds').textContent = '-$13.67';
                nextCard.querySelector('.net-earnings').textContent = '$31,130.51';
                nextCard.querySelector('.after-taxes').textContent = '$29,130.51';
                nextCard.querySelector('.units-charged').textContent = '188';
                nextCard.querySelector('.units-refunded').textContent = '7';
            }

            const prevCard = document.querySelector('.previous-payout-card-div');
            if (prevCard) {
                prevCard.querySelector('.gross-earnings').textContent = '$298.45';
                prevCard.querySelector('.refunds').textContent = '-$11.23';
                prevCard.querySelector('.net-earnings').textContent = '$28,945.33';
                prevCard.querySelector('.after-taxes').textContent = '$26,945.33';
                prevCard.querySelector('.units-charged').textContent = '165';
                prevCard.querySelector('.units-refunded').textContent = '5';
            }

            // Reset lifetime insights
            const lifetimeCard = document.querySelector('.lifetime-insights-card-div');
            if (lifetimeCard) {
                // Reset sales count in sales-analytics-div
                const salesCount = lifetimeCard.querySelector('.sales-analytics-div .sales-count');
                if (salesCount) salesCount.textContent = '217,223';
                
                // Reset metric values in sales-analytics-div
                const metricValues = lifetimeCard.querySelectorAll('.sales-analytics-div .analytics-div .metric-value');
                const originalMetricValues = ['$511,933.0', '17,099', '2,231', '65,000'];
                metricValues.forEach((el, index) => {
                    if (originalMetricValues[index]) el.textContent = originalMetricValues[index];
                });
                
                // Reset metric percentages in sales-analytics-div
                const metricPercentages = lifetimeCard.querySelectorAll('.sales-analytics-div .analytics-div .metric-percentage');
                const originalPercentages = ['7.9%', '29.9%'];
                metricPercentages.forEach((el, index) => {
                    if (originalPercentages[index]) el.textContent = originalPercentages[index];
                });
                
                // Reset lifetime data values
                const dataValues = lifetimeCard.querySelectorAll('.lifetime-data-value');
                const originalValues = ['$1,571,933.0', '$511,933.0', '$571,933.0', '-$61,933', '-$61,933', '$3.93'];
                dataValues.forEach((el, index) => {
                    if (originalValues[index]) el.textContent = originalValues[index];
                });
                
                const taxRate = lifetimeCard.querySelector('.lifetime-tax-rate');
                if (taxRate) taxRate.textContent = '30%';
            }

            console.log('✅ Reset to original mock data');
        }

        function inspectComponents() {
            const results = [];
            
            // Inspect payout cards
            const payoutCards = document.querySelectorAll('.next-payout-card-div, .previous-payout-card-div');
            payoutCards.forEach((card, index) => {
                const cardType = card.classList.contains('next-payout-card-div') ? 'Next Payout' : 'Previous Payout';
                results.push(`\n=== ${cardType} Card ===`);
                
                const emptyState = card.querySelector('.no-payout-state');
                const payoutDataDiv = card.querySelector('.payout-data-div');
                
                results.push(`Empty State Display: ${emptyState ? emptyState.style.display : 'not found'}`);
                results.push(`Payout Data Display: ${payoutDataDiv ? payoutDataDiv.style.display : 'not found'}`);
                
                const dataValues = card.querySelectorAll('.payout-data-value');
                dataValues.forEach(el => {
                    const label = el.parentNode.querySelector('.payout-data-label')?.textContent || 'unknown';
                    results.push(`${label}: ${el.textContent}`);
                });
                
                const marketplaceTotals = card.querySelectorAll('.marketplace-total-sales-count, .marketplace-total-earned-royalties, .marketplace-total-returned-units');
                marketplaceTotals.forEach((el, i) => {
                    const type = el.classList.contains('marketplace-total-sales-count') ? 'Sales' :
                                el.classList.contains('marketplace-total-earned-royalties') ? 'Royalties' : 'Returns';
                    results.push(`Marketplace ${type}: ${el.textContent}`);
                });
            });
            
            // Inspect lifetime insights
            const lifetimeCard = document.querySelector('.lifetime-insights-card-div');
            if (lifetimeCard) {
                results.push(`\n=== Lifetime Insights Card ===`);
                
                // Check sales-analytics-div elements
                const salesCount = lifetimeCard.querySelector('.sales-analytics-div .sales-count');
                if (salesCount) results.push(`Sales Analytics Count: ${salesCount.textContent}`);
                
                const metricValues = lifetimeCard.querySelectorAll('.sales-analytics-div .analytics-div .metric-value');
                metricValues.forEach(el => {
                    const metricCol = el.closest('.metric-col');
                    const label = metricCol ? metricCol.className.match(/(\w+)-metric/)?.[1] || 'unknown' : 'unknown';
                    results.push(`Sales Analytics ${label}: ${el.textContent}`);
                });
                
                const metricPercentages = lifetimeCard.querySelectorAll('.sales-analytics-div .analytics-div .metric-percentage');
                metricPercentages.forEach(el => {
                    const metricCol = el.closest('.metric-col');
                    const label = metricCol ? metricCol.className.match(/(\w+)-metric/)?.[1] || 'unknown' : 'unknown';
                    results.push(`Sales Analytics ${label} %: ${el.textContent}`);
                });
                
                // Check lifetime-data-div elements
                const dataValues = lifetimeCard.querySelectorAll('.lifetime-data-value');
                dataValues.forEach(el => {
                    const label = el.parentNode.querySelector('.lifetime-data-label')?.textContent || 'unknown';
                    results.push(`Lifetime Data ${label}: ${el.textContent}`);
                });
                
                const taxRate = lifetimeCard.querySelector('.lifetime-tax-rate');
                if (taxRate) results.push(`Tax Rate: ${taxRate.textContent}`);
                
                const marketplaceTotals = lifetimeCard.querySelectorAll('.marketplace-total-sales-count, .marketplace-total-earned-royalties, .marketplace-total-returned-units');
                marketplaceTotals.forEach((el, i) => {
                    const type = el.classList.contains('marketplace-total-sales-count') ? 'Sales' :
                                el.classList.contains('marketplace-total-earned-royalties') ? 'Royalties' : 'Returns';
                    results.push(`Lifetime Marketplace ${type}: ${el.textContent}`);
                });
            }
            
            const resultsEl = document.getElementById('inspection-results');
            resultsEl.textContent = results.join('\n');
            resultsEl.style.display = 'block';
        }
    </script>
</body>
</html>
