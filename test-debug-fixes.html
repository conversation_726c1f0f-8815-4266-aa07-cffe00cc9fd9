<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Fixes Test Suite</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-header {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .test-pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .test-section {
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1>🔧 Debug Fixes Test Suite</h1>
    <p>This test suite validates all the fixes implemented from the debugging report.</p>

    <div class="test-section">
        <div class="test-container">
            <div class="test-header">🔄 Real-Time Data Key Mapping Test</div>
            <button onclick="testRealTimeKeyMapping()">Test Key Mapping</button>
            <div id="realtime-test-results"></div>
        </div>

        <div class="test-container">
            <div class="test-header">🎯 Tooltip Parameter Order Test</div>
            <button onclick="testTooltipParameters()">Test Tooltip Parameters</button>
            <div id="tooltip-test-results"></div>
        </div>

        <div class="test-container">
            <div class="test-header">⏰ Timer Cleanup Test</div>
            <button onclick="testTimerCleanup()">Test Timer Cleanup</button>
            <div id="timer-test-results"></div>
        </div>

        <div class="test-container">
            <div class="test-header">🔒 XSS Protection Test</div>
            <button onclick="testXSSProtection()">Test XSS Protection</button>
            <div id="xss-test-results"></div>
        </div>

        <div class="test-container">
            <div class="test-header">⚡ Performance Optimization Test</div>
            <button onclick="testPerformanceOptimizations()">Test Performance</button>
            <div id="performance-test-results"></div>
        </div>

        <div class="test-container">
            <div class="test-header">🔢 Units Parsing Test</div>
            <button onclick="testUnitsParser()">Test Units Parser</button>
            <div id="units-test-results"></div>
        </div>

        <div class="test-container">
            <div class="test-header">📊 View Insights Modal Test</div>
            <button onclick="testViewInsights()">Test View Insights</button>
            <div id="insights-test-results"></div>
        </div>

        <div class="test-container">
            <div class="test-header">📝 Logger System Test</div>
            <button onclick="testLoggerSystem()">Test Logger</button>
            <div id="logger-test-results"></div>
        </div>

        <div class="test-container">
            <div class="test-header">🧪 Run All Tests</div>
            <button onclick="runAllTests()" style="background: #28a745;">Run Complete Test Suite</button>
            <div id="all-tests-results"></div>
        </div>
    </div>

    <script>
        // Test utilities
        function addTestResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result test-${type}`;
            result.textContent = message;
            container.appendChild(result);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        // Test 1: Real-Time Data Key Mapping
        function testRealTimeKeyMapping() {
            clearResults('realtime-test-results');
            addTestResult('realtime-test-results', 'Testing real-time data key mapping...', 'info');

            try {
                // Check if RealTimeDataManager exists
                if (window.RealTimeDataManager) {
                    addTestResult('realtime-test-results', '✅ RealTimeDataManager found', 'pass');
                    
                    // Test the updateUI method with mock data
                    const manager = window.RealTimeDataManager;
                    const originalHandler = window.handleRealTimeDataUpdate;
                    let capturedData = null;
                    
                    // Mock the handler to capture data
                    window.handleRealTimeDataUpdate = function(data) {
                        capturedData = data;
                    };
                    
                    // Test key mapping
                    manager.updateUI('salesData', [{test: 'data'}]);
                    
                    if (capturedData && capturedData.analytics) {
                        addTestResult('realtime-test-results', '✅ salesData → analytics mapping works', 'pass');
                    } else {
                        addTestResult('realtime-test-results', '❌ salesData mapping failed', 'fail');
                    }
                    
                    // Restore original handler
                    window.handleRealTimeDataUpdate = originalHandler;
                } else {
                    addTestResult('realtime-test-results', '⚠️ RealTimeDataManager not loaded', 'fail');
                }
            } catch (error) {
                addTestResult('realtime-test-results', `❌ Error: ${error.message}`, 'fail');
            }
        }

        // Test 2: Tooltip Parameters
        function testTooltipParameters() {
            clearResults('tooltip-test-results');
            addTestResult('tooltip-test-results', 'Testing tooltip parameter order...', 'info');

            try {
                // Check if SnapChart exists and has showTooltip method
                if (window.SnapChart) {
                    addTestResult('tooltip-test-results', '✅ SnapChart class found', 'pass');
                    
                    // Check method signature
                    const chart = new window.SnapChart();
                    if (typeof chart.showTooltip === 'function') {
                        addTestResult('tooltip-test-results', '✅ showTooltip method exists', 'pass');
                        
                        // Test parameter count (should accept 4 parameters: event, dataPoint, index, comparisonDataPoint)
                        const methodStr = chart.showTooltip.toString();
                        if (methodStr.includes('index') && methodStr.includes('comparisonDataPoint')) {
                            addTestResult('tooltip-test-results', '✅ Method signature includes index and comparisonDataPoint', 'pass');
                        } else {
                            addTestResult('tooltip-test-results', '⚠️ Method signature may be incorrect', 'fail');
                        }
                    } else {
                        addTestResult('tooltip-test-results', '❌ showTooltip method not found', 'fail');
                    }
                } else {
                    addTestResult('tooltip-test-results', '⚠️ SnapChart not loaded', 'fail');
                }
            } catch (error) {
                addTestResult('tooltip-test-results', `❌ Error: ${error.message}`, 'fail');
            }
        }

        // Test 3: Timer Cleanup
        function testTimerCleanup() {
            clearResults('timer-test-results');
            addTestResult('timer-test-results', 'Testing timer cleanup system...', 'info');

            try {
                if (window.EventCleanupManager) {
                    addTestResult('timer-test-results', '✅ EventCleanupManager found', 'pass');
                    
                    // Test setTimeout and clearTimeout
                    const timeoutId = window.EventCleanupManager.setTimeout(() => {}, 1000);
                    if (timeoutId) {
                        addTestResult('timer-test-results', '✅ setTimeout works', 'pass');
                        
                        window.EventCleanupManager.clearTimeout(timeoutId);
                        addTestResult('timer-test-results', '✅ clearTimeout works', 'pass');
                    }
                    
                    // Test setInterval and clearInterval
                    const intervalId = window.EventCleanupManager.setInterval(() => {}, 1000);
                    if (intervalId) {
                        addTestResult('timer-test-results', '✅ setInterval works', 'pass');
                        
                        window.EventCleanupManager.clearInterval(intervalId);
                        addTestResult('timer-test-results', '✅ clearInterval works', 'pass');
                    }
                    
                    // Test debounced function
                    if (window.createDebouncedFunction) {
                        const debouncedFn = window.createDebouncedFunction(() => {}, 100);
                        if (typeof debouncedFn === 'function') {
                            addTestResult('timer-test-results', '✅ createDebouncedFunction works', 'pass');
                        }
                    }
                } else {
                    addTestResult('timer-test-results', '❌ EventCleanupManager not found', 'fail');
                }
            } catch (error) {
                addTestResult('timer-test-results', `❌ Error: ${error.message}`, 'fail');
            }
        }

        // Test 4: XSS Protection
        function testXSSProtection() {
            clearResults('xss-test-results');
            addTestResult('xss-test-results', 'Testing XSS protection...', 'info');

            try {
                // Test createSafeElementFromTrustedHTML function
                if (typeof createSafeElementFromTrustedHTML === 'function') {
                    addTestResult('xss-test-results', '✅ createSafeElementFromTrustedHTML function found', 'pass');
                    
                    // Test with safe HTML
                    const safeHTML = '<div class="test-class">Safe content</div>';
                    const element = createSafeElementFromTrustedHTML(safeHTML, 'test-class');
                    
                    if (element && element.classList.contains('test-class')) {
                        addTestResult('xss-test-results', '✅ Safe HTML processing works', 'pass');
                    } else {
                        addTestResult('xss-test-results', '❌ Safe HTML processing failed', 'fail');
                    }
                    
                    // Test with potentially unsafe HTML (should be rejected)
                    const unsafeHTML = '<div class="wrong-class">Content</div>';
                    const unsafeElement = createSafeElementFromTrustedHTML(unsafeHTML, 'test-class');
                    
                    if (!unsafeElement) {
                        addTestResult('xss-test-results', '✅ Unsafe HTML properly rejected', 'pass');
                    } else {
                        addTestResult('xss-test-results', '⚠️ Unsafe HTML not properly rejected', 'fail');
                    }
                } else {
                    addTestResult('xss-test-results', '⚠️ XSS protection function not accessible in global scope', 'fail');
                }
            } catch (error) {
                addTestResult('xss-test-results', `❌ Error: ${error.message}`, 'fail');
            }
        }

        // Test 5: Performance Optimizations
        function testPerformanceOptimizations() {
            clearResults('performance-test-results');
            addTestResult('performance-test-results', 'Testing performance optimizations...', 'info');

            try {
                // Test debounced updateNewTabCount
                if (typeof updateNewTabCount === 'function') {
                    addTestResult('performance-test-results', '✅ updateNewTabCount function found', 'pass');
                    
                    // Check if it's debounced (should be a function returned by createDebouncedFunction)
                    const fnStr = updateNewTabCount.toString();
                    if (fnStr.includes('createDebouncedFunction') || fnStr.length < 100) {
                        addTestResult('performance-test-results', '✅ updateNewTabCount appears to be debounced', 'pass');
                    } else {
                        addTestResult('performance-test-results', '⚠️ updateNewTabCount may not be debounced', 'fail');
                    }
                } else {
                    addTestResult('performance-test-results', '⚠️ updateNewTabCount function not accessible', 'fail');
                }
                
                // Test refreshAllUIComponents optimization
                if (typeof refreshAllUIComponents === 'function') {
                    addTestResult('performance-test-results', '✅ refreshAllUIComponents function found', 'pass');
                } else {
                    addTestResult('performance-test-results', '⚠️ refreshAllUIComponents function not accessible', 'fail');
                }
            } catch (error) {
                addTestResult('performance-test-results', `❌ Error: ${error.message}`, 'fail');
            }
        }

        // Test 6: Units Parser
        function testUnitsParser() {
            clearResults('units-test-results');
            addTestResult('units-test-results', 'Testing units parser with thousands separators...', 'info');

            try {
                if (typeof extractUnitsValue === 'function') {
                    addTestResult('units-test-results', '✅ extractUnitsValue function found', 'pass');
                    
                    // Create mock DOM elements for testing
                    const mockListing = document.createElement('div');
                    const unitsSpan = document.createElement('span');
                    const unitsBadge = document.createElement('div');
                    unitsBadge.className = 'order-units-badge';
                    unitsBadge.appendChild(unitsSpan);
                    mockListing.appendChild(unitsBadge);
                    
                    // Test with comma-separated thousands
                    unitsSpan.textContent = '+1,234';
                    const result1 = extractUnitsValue(mockListing);
                    if (result1 === 1234) {
                        addTestResult('units-test-results', '✅ Comma-separated thousands parsing works', 'pass');
                    } else {
                        addTestResult('units-test-results', `❌ Expected 1234, got ${result1}`, 'fail');
                    }
                    
                    // Test with regular numbers
                    unitsSpan.textContent = '+567';
                    const result2 = extractUnitsValue(mockListing);
                    if (result2 === 567) {
                        addTestResult('units-test-results', '✅ Regular number parsing works', 'pass');
                    } else {
                        addTestResult('units-test-results', `❌ Expected 567, got ${result2}`, 'fail');
                    }
                } else {
                    addTestResult('units-test-results', '⚠️ extractUnitsValue function not accessible', 'fail');
                }
            } catch (error) {
                addTestResult('units-test-results', `❌ Error: ${error.message}`, 'fail');
            }
        }

        // Test 7: View Insights Modal
        function testViewInsights() {
            clearResults('insights-test-results');
            addTestResult('insights-test-results', 'Testing View Insights modal system...', 'info');

            try {
                if (typeof createInsightsModal === 'function') {
                    addTestResult('insights-test-results', '✅ createInsightsModal function found', 'pass');
                    
                    // Test modal creation
                    createInsightsModal('sales', 'Test Period');
                    
                    const modal = document.getElementById('insights-modal');
                    if (modal) {
                        addTestResult('insights-test-results', '✅ Modal created successfully', 'pass');
                        
                        // Test modal content
                        if (modal.textContent.includes('Test Period')) {
                            addTestResult('insights-test-results', '✅ Modal displays correct period', 'pass');
                        }
                        
                        // Clean up
                        modal.remove();
                        addTestResult('insights-test-results', '✅ Modal cleanup works', 'pass');
                    } else {
                        addTestResult('insights-test-results', '❌ Modal not created', 'fail');
                    }
                } else {
                    addTestResult('insights-test-results', '⚠️ createInsightsModal function not accessible', 'fail');
                }
            } catch (error) {
                addTestResult('insights-test-results', `❌ Error: ${error.message}`, 'fail');
            }
        }

        // Test 8: Logger System
        function testLoggerSystem() {
            clearResults('logger-test-results');
            addTestResult('logger-test-results', 'Testing logger system...', 'info');

            try {
                if (window.SnapLogger) {
                    addTestResult('logger-test-results', '✅ SnapLogger found', 'pass');
                    
                    // Test logger methods
                    if (typeof window.SnapLogger.debug === 'function') {
                        addTestResult('logger-test-results', '✅ debug method exists', 'pass');
                    }
                    if (typeof window.SnapLogger.info === 'function') {
                        addTestResult('logger-test-results', '✅ info method exists', 'pass');
                    }
                    if (typeof window.SnapLogger.warn === 'function') {
                        addTestResult('logger-test-results', '✅ warn method exists', 'pass');
                    }
                    if (typeof window.SnapLogger.error === 'function') {
                        addTestResult('logger-test-results', '✅ error method exists', 'pass');
                    }
                    if (typeof window.SnapLogger.setLevel === 'function') {
                        addTestResult('logger-test-results', '✅ setLevel method exists', 'pass');
                    }
                    
                    // Test level setting
                    const originalLevel = window.SnapLogger.level;
                    window.SnapLogger.setLevel('warn');
                    if (window.SnapLogger.level === 'warn') {
                        addTestResult('logger-test-results', '✅ Level setting works', 'pass');
                    }
                    window.SnapLogger.setLevel(originalLevel);
                } else {
                    addTestResult('logger-test-results', '❌ SnapLogger not found', 'fail');
                }
            } catch (error) {
                addTestResult('logger-test-results', `❌ Error: ${error.message}`, 'fail');
            }
        }

        // Run all tests
        function runAllTests() {
            clearResults('all-tests-results');
            addTestResult('all-tests-results', '🚀 Running complete test suite...', 'info');
            
            setTimeout(() => testRealTimeKeyMapping(), 100);
            setTimeout(() => testTooltipParameters(), 200);
            setTimeout(() => testTimerCleanup(), 300);
            setTimeout(() => testXSSProtection(), 400);
            setTimeout(() => testPerformanceOptimizations(), 500);
            setTimeout(() => testUnitsParser(), 600);
            setTimeout(() => testViewInsights(), 700);
            setTimeout(() => testLoggerSystem(), 800);
            
            setTimeout(() => {
                addTestResult('all-tests-results', '✅ All tests completed! Check individual sections for results.', 'pass');
            }, 1000);
        }

        // Check if dashboard components are loaded
        function checkDashboardLoaded() {
            const checks = [
                { name: 'RealTimeDataManager', exists: !!window.RealTimeDataManager },
                { name: 'EventCleanupManager', exists: !!window.EventCleanupManager },
                { name: 'SnapChart', exists: !!window.SnapChart },
                { name: 'SnapLogger', exists: !!window.SnapLogger },
                { name: 'createDebouncedFunction', exists: !!window.createDebouncedFunction },
                { name: 'extractUnitsValue', exists: !!window.extractUnitsValue },
                { name: 'createInsightsModal', exists: !!window.createInsightsModal },
                { name: 'createSafeElementFromTrustedHTML', exists: !!window.createSafeElementFromTrustedHTML }
            ];

            const loaded = checks.filter(c => c.exists);
            const missing = checks.filter(c => !c.exists);

            console.log(`✅ Loaded: ${loaded.map(c => c.name).join(', ')}`);
            if (missing.length > 0) {
                console.log(`❌ Missing: ${missing.map(c => c.name).join(', ')}`);
            }

            return missing.length === 0;
        }

        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            console.log('🧪 Debug Fixes Test Suite loaded');

            setTimeout(() => {
                console.log('🔍 Checking if dashboard components are loaded...');
                if (checkDashboardLoaded()) {
                    console.log('✅ All components loaded! You can run tests now.');
                } else {
                    console.log('⚠️ Some components are missing. Make sure the dashboard is loaded first.');
                    console.log('💡 Try loading the main dashboard page, then come back to run tests.');
                }
            }, 1000);
        });
    </script>
</body>
</html>
