<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Payout Empty State</title>
  <link rel="stylesheet" href="snapapp.css">
  <style>
    body {
      font-family: 'Amazon Ember', sans-serif;
      margin: 20px;
      background: #f5f5f5;
    }
    .test-container {
      max-width: 500px;
      margin: 0 auto;
      background: white;
      border-radius: 14px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .test-button {
      margin: 20px 0;
      padding: 10px 20px;
      background: #04AE2C;
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
    }
    .test-button:hover {
      background: #038a23;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h2>Previous Payout Card - Empty State Test</h2>
    
    <button class="test-button" onclick="toggleEmptyState()">Toggle Empty State</button>
    
    <!-- Previous Payout Card Structure -->
    <div class="previous-payout-card-div">
      <div class="payout-title-date-div">
        <div class="title-date-section">
          <img src="./assets/payout-ic.svg" alt="Payout Icon" class="payout-card-icon" width="16" height="16" />
          <div class="title-date-text">
            <span class="payout-card-title">Previous Payout</span>
          </div>
        </div>
        <div class="payout-date">
          <span>April 2025</span>
        </div>
      </div>
      
      <div class="marketplaces-div">
        <div class="marketplaces-payout-row">
          <div class="marketplace-col us active" data-tooltip="United States">
            <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-payout-amount">$28,945.33</span>
          </div>
          <div class="marketplace-col uk inactive" data-tooltip="United Kingdom">
            <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-payout-amount">£1,205.5</span>
          </div>
          <div class="marketplace-col jp inactive" data-tooltip="Japan">
            <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-payout-amount">¥0</span>
          </div>
        </div>
      </div>
      
      <hr class="sales-section-divider" />
      
      <!-- Empty State -->
      <div class="no-payout-state" style="display: none;">
        <img src="./assets/no-payout-img.svg" alt="No Payout" class="no-payout-img" width="98" height="109" />
        <div class="no-payout-text">
          <div class="no-payout-title">No payout available, yet.</div>
          <div class="no-payout-subtitle">Check back soon!</div>
        </div>
      </div>
      
      <div class="payout-data-div">
        <div class="payout-data-item">
          <span class="payout-data-label">Gross Earnings</span>
          <span class="payout-data-value gross-earnings">$298.45</span>
        </div>
        <div class="payout-data-item">
          <span class="payout-data-label">Tax Withheld</span>
          <span class="payout-data-value tax-withheld negative">-$45.67</span>
        </div>
        <div class="payout-data-item">
          <span class="payout-data-label">Net Earnings</span>
          <span class="payout-data-value net-earnings">$252.78</span>
        </div>
      </div>
    </div>
  </div>

  <script>
    function toggleEmptyState() {
      const emptyState = document.querySelector('.no-payout-state');
      const marketplacesDiv = document.querySelector('.marketplaces-div');
      const payoutDataDiv = document.querySelector('.payout-data-div');
      const divider = document.querySelector('.sales-section-divider');
      
      const isEmptyVisible = emptyState.style.display === 'flex';
      
      if (isEmptyVisible) {
        // Show normal state
        emptyState.style.display = 'none';
        marketplacesDiv.style.display = '';
        payoutDataDiv.style.display = '';
        divider.style.display = '';
      } else {
        // Show empty state
        emptyState.style.display = 'flex';
        marketplacesDiv.style.display = 'none';
        payoutDataDiv.style.display = 'none';
        divider.style.display = 'none';
      }
    }

    // Add marketplace selection functionality
    document.addEventListener('DOMContentLoaded', function() {
      const marketplaceCols = document.querySelectorAll('.marketplace-col');
      
      marketplaceCols.forEach(col => {
        col.addEventListener('click', function() {
          // Remove active class from all columns
          marketplaceCols.forEach(c => {
            c.classList.remove('active');
            c.classList.add('inactive');
          });
          
          // Add active class to clicked column
          this.classList.remove('inactive');
          this.classList.add('active');
          
          // Check if this is Japan (jp) - simulate empty state
          if (this.classList.contains('jp')) {
            // Show empty state for Japan
            const emptyState = document.querySelector('.no-payout-state');
            const marketplacesDiv = document.querySelector('.marketplaces-div');
            const payoutDataDiv = document.querySelector('.payout-data-div');
            const divider = document.querySelector('.sales-section-divider');
            
            if (emptyState) emptyState.style.display = 'flex';
            if (payoutDataDiv) payoutDataDiv.style.display = 'none';
            if (divider) divider.style.display = 'none';
          } else {
            // Show normal state for other marketplaces
            const emptyState = document.querySelector('.no-payout-state');
            const marketplacesDiv = document.querySelector('.marketplaces-div');
            const payoutDataDiv = document.querySelector('.payout-data-div');
            const divider = document.querySelector('.sales-section-divider');
            
            if (emptyState) emptyState.style.display = 'none';
            if (payoutDataDiv) payoutDataDiv.style.display = '';
            if (divider) divider.style.display = '';
          }
        });
      });
    });
  </script>
</body>
</html>
