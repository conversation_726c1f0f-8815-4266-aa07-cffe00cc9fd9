<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Zero Data Columns Fix</title>
    <link rel="stylesheet" href="snapapp.css">
    <link rel="stylesheet" href="components/charts/snap-charts.css">
    <style>
        body {
            font-family: 'Amazon Ember', sans-serif;
            margin: 20px;
            background-color: var(--bg-primary);
            color: var(--text-primary);
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            margin: 40px 0;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: var(--text-primary);
        }
        .test-description {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 20px;
        }
        .chart-container {
            width: 100%;
            height: 300px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 20px 0;
        }
        .test-buttons {
            margin: 20px 0;
        }
        .test-button {
            background-color: var(--accent-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            font-size: 14px;
        }
        .test-button:hover {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Test: Zero Data Columns Fix</h1>
        <p>This test verifies that columns with all zero segments still show sales and returns values as "0" and "(0)" respectively, while hiding the visual column representation.</p>

        <div class="test-section">
            <div class="test-title">Test 1: Mixed Data with Some Zero Columns</div>
            <div class="test-description">
                This chart has some columns with data and some with all zero segments. 
                Zero columns should show "0" for sales and "(0)" for returns, but no visual column.
            </div>
            <div id="chart1" class="chart-container"></div>
        </div>

        <div class="test-section">
            <div class="test-title">Test 2: All Zero Data</div>
            <div class="test-description">
                This chart has all columns with zero data. All columns should show "0" and "(0)" values with no visual columns.
            </div>
            <div id="chart2" class="chart-container"></div>
        </div>

        <div class="test-section">
            <div class="test-title">Test 3: Comparison Mode with Zero Data</div>
            <div class="test-description">
                This chart tests comparison mode with zero data columns.
            </div>
            <div id="chart3" class="chart-container"></div>
        </div>

        <div class="test-buttons">
            <button class="test-button" onclick="runTests()">Run All Tests</button>
            <button class="test-button" onclick="toggleReturns()">Toggle Returns Display</button>
        </div>
    </div>

    <!-- Load Chart Dependencies -->
    <script src="components/charts/snap-charts.js"></script>
    
    <script>
        let showReturns = true;
        let charts = [];

        function createMixedData() {
            return [
                {
                    month: 'JAN', day: '1', year: '2024',
                    sales: 150, royalties: 15.0, returns: 5,
                    values: [50, 100, 0], // Third segment is zero
                    labels: ['US', 'UK', 'DE'],
                    marketplaces: [
                        { code: 'US', sales: 50, royalties: 5.0, returns: 2 },
                        { code: 'UK', sales: 100, royalties: 10.0, returns: 3 },
                        { code: 'DE', sales: 0, royalties: 0, returns: 0 }
                    ]
                },
                {
                    month: 'FEB', day: '1', year: '2024',
                    sales: 0, royalties: 0, returns: 0, // ALL ZERO - should show "0" and "(0)"
                    values: [0, 0, 0],
                    labels: ['US', 'UK', 'DE'],
                    marketplaces: [
                        { code: 'US', sales: 0, royalties: 0, returns: 0 },
                        { code: 'UK', sales: 0, royalties: 0, returns: 0 },
                        { code: 'DE', sales: 0, royalties: 0, returns: 0 }
                    ]
                },
                {
                    month: 'MAR', day: '1', year: '2024',
                    sales: 200, royalties: 20.0, returns: 8,
                    values: [200, 0, 0], // Only first segment has data
                    labels: ['US', 'UK', 'DE'],
                    marketplaces: [
                        { code: 'US', sales: 200, royalties: 20.0, returns: 8 },
                        { code: 'UK', sales: 0, royalties: 0, returns: 0 },
                        { code: 'DE', sales: 0, royalties: 0, returns: 0 }
                    ]
                },
                {
                    month: 'APR', day: '1', year: '2024',
                    sales: 0, royalties: 0, returns: 0, // ALL ZERO - should show "0" and "(0)"
                    values: [0, 0, 0],
                    labels: ['US', 'UK', 'DE'],
                    marketplaces: [
                        { code: 'US', sales: 0, royalties: 0, returns: 0 },
                        { code: 'UK', sales: 0, royalties: 0, returns: 0 },
                        { code: 'DE', sales: 0, royalties: 0, returns: 0 }
                    ]
                },
                {
                    month: 'MAY', day: '1', year: '2024',
                    sales: 75, royalties: 7.5, returns: 2,
                    values: [0, 75, 0], // Only middle segment has data
                    labels: ['US', 'UK', 'DE'],
                    marketplaces: [
                        { code: 'US', sales: 0, royalties: 0, returns: 0 },
                        { code: 'UK', sales: 75, royalties: 7.5, returns: 2 },
                        { code: 'DE', sales: 0, royalties: 0, returns: 0 }
                    ]
                }
            ];
        }

        function createAllZeroData() {
            return [
                {
                    month: 'JAN', day: '1', year: '2024',
                    sales: 0, royalties: 0, returns: 0,
                    values: [0, 0, 0],
                    labels: ['US', 'UK', 'DE'],
                    marketplaces: [
                        { code: 'US', sales: 0, royalties: 0, returns: 0 },
                        { code: 'UK', sales: 0, royalties: 0, returns: 0 },
                        { code: 'DE', sales: 0, royalties: 0, returns: 0 }
                    ]
                },
                {
                    month: 'FEB', day: '1', year: '2024',
                    sales: 0, royalties: 0, returns: 0,
                    values: [0, 0, 0],
                    labels: ['US', 'UK', 'DE'],
                    marketplaces: [
                        { code: 'US', sales: 0, royalties: 0, returns: 0 },
                        { code: 'UK', sales: 0, royalties: 0, returns: 0 },
                        { code: 'DE', sales: 0, royalties: 0, returns: 0 }
                    ]
                },
                {
                    month: 'MAR', day: '1', year: '2024',
                    sales: 0, royalties: 0, returns: 0,
                    values: [0, 0, 0],
                    labels: ['US', 'UK', 'DE'],
                    marketplaces: [
                        { code: 'US', sales: 0, royalties: 0, returns: 0 },
                        { code: 'UK', sales: 0, royalties: 0, returns: 0 },
                        { code: 'DE', sales: 0, royalties: 0, returns: 0 }
                    ]
                }
            ];
        }

        function createComparisonData() {
            return {
                main: [
                    {
                        month: 'JAN', day: '1', year: '2024',
                        sales: 0, royalties: 0, returns: 0,
                        values: [0, 0, 0],
                        labels: ['US', 'UK', 'DE']
                    },
                    {
                        month: 'FEB', day: '1', year: '2024',
                        sales: 100, royalties: 10, returns: 3,
                        values: [100, 0, 0],
                        labels: ['US', 'UK', 'DE']
                    }
                ],
                compare: [
                    {
                        month: 'JAN', day: '1', year: '2023',
                        sales: 50, royalties: 5, returns: 1,
                        values: [50, 0, 0],
                        labels: ['US', 'UK', 'DE']
                    },
                    {
                        month: 'FEB', day: '1', year: '2023',
                        sales: 0, royalties: 0, returns: 0,
                        values: [0, 0, 0],
                        labels: ['US', 'UK', 'DE']
                    }
                ]
            };
        }

        function runTests() {
            console.log('🧪 Running zero data columns tests...');
            
            // Clear existing charts
            charts.forEach(chart => {
                if (chart && chart.destroy) {
                    chart.destroy();
                }
            });
            charts = [];

            // Test 1: Mixed data
            const chart1 = new SnapChart({
                container: document.getElementById('chart1'),
                type: 'stacked-column',
                showReturns: showReturns
            });
            chart1.setData(createMixedData());
            chart1.render();
            charts.push(chart1);

            // Test 2: All zero data
            const chart2 = new SnapChart({
                container: document.getElementById('chart2'),
                type: 'stacked-column',
                showReturns: showReturns
            });
            chart2.setData(createAllZeroData());
            chart2.render();
            charts.push(chart2);

            // Test 3: Comparison with zero data
            const comparisonData = createComparisonData();
            const chart3 = new SnapChart({
                container: document.getElementById('chart3'),
                type: 'stacked-column',
                showReturns: showReturns,
                compareData: comparisonData.compare
            });
            chart3.setData(comparisonData.main);
            chart3.render();
            charts.push(chart3);

            console.log('✅ All tests completed!');
            console.log('📊 Check that:');
            console.log('  - Columns with all zero segments show no visual column');
            console.log('  - But sales values still show "0"');
            console.log('  - And returns values still show "(0)" when enabled');
        }

        function toggleReturns() {
            showReturns = !showReturns;
            console.log(`🔄 Returns display: ${showReturns ? 'ON' : 'OFF'}`);
            runTests(); // Re-run tests with new setting
        }

        // Run tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            runTests();
        });
    </script>
</body>
</html>
